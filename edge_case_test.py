#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
边界情况和特殊场景测试
"""

import os
import re
import tempfile

def extract_size_from_target_mapping(target_mapping):
    """从目标映射字符串中提取尺寸信息"""
    if not target_mapping:
        return None, None
    
    size_patterns = [
        r'(\d+)x(\d+)cm',  # 40x60cm
        r'(\d+)X(\d+)cm',  # 40X60cm
        r'(\d+)×(\d+)cm',  # 40×60cm
    ]
    
    for pattern in size_patterns:
        match = re.search(pattern, target_mapping)
        if match:
            width = int(match.group(1))
            height = int(match.group(2))
            return width, height
    
    return None, None

def get_size_suffix_from_dimensions(width, height):
    """根据尺寸生成对应的文件名后缀"""
    if width is None or height is None:
        return None
    return f"-{width:02d}{height:02d}"

def is_multi_pc_image(filename, skc_base):
    """判断图片是否为多PC格式"""
    if not filename or not skc_base:
        return False
    
    filename_lower = filename.lower()
    skc_lower = skc_base.lower()
    
    if not filename_lower.startswith(skc_lower):
        return False
    
    suffix = filename_lower[len(skc_lower):]
    multi_pc_pattern = r'^-\d+-\d+(?:-\d+)*(?:-?\d{4})?\.jpg$'
    
    return bool(re.match(multi_pc_pattern, suffix))

def find_images_by_skc_with_size_filter(source_folder, skc_base, target_mapping=None):
    """查找SKC相关的图片文件，支持单图片多规格的智能筛选"""
    if not os.path.isdir(source_folder):
        return []
    
    # 查找所有SKC相关的图片
    all_skc_images = []
    for filename in os.listdir(source_folder):
        if (filename.lower().startswith(skc_base.lower()) and 
            filename.lower().endswith('.jpg') and 
            os.path.isfile(os.path.join(source_folder, filename))):
            all_skc_images.append(filename)
    
    if not all_skc_images:
        return []
    
    # 分类图片
    base_images = []  # skc.jpg
    single_pc_size_images = []  # skc-4060.jpg, skc-5070.jpg
    multi_pc_images = []  # skc-1-2-4060.jpg
    
    for filename in all_skc_images:
        if is_multi_pc_image(filename, skc_base):
            multi_pc_images.append(filename)
        elif filename.lower() == f"{skc_base.lower()}.jpg":
            base_images.append(filename)
        else:
            suffix = filename.lower()[len(skc_base.lower()):]
            if re.match(r'^-\d{4}\.jpg$', suffix):
                single_pc_size_images.append(filename)
            else:
                single_pc_size_images.append(filename)
    
    # 如果有多PC图片，优先返回多PC图片
    if multi_pc_images:
        return [os.path.join(source_folder, f) for f in multi_pc_images]
    
    # 如果只有基础图片，直接返回
    if base_images and not single_pc_size_images:
        return [os.path.join(source_folder, f) for f in base_images]
    
    # 如果有单PC多规格图片，根据target_mapping筛选
    if single_pc_size_images:
        if not target_mapping:
            return [os.path.join(source_folder, f) for f in base_images + single_pc_size_images]
        
        # 从目标映射中提取期望的尺寸
        expected_width, expected_height = extract_size_from_target_mapping(target_mapping)
        
        if expected_width is None or expected_height is None:
            return [os.path.join(source_folder, f) for f in base_images + single_pc_size_images]
        
        # 生成期望的后缀
        expected_suffix = get_size_suffix_from_dimensions(expected_width, expected_height)
        
        if expected_suffix:
            # 查找匹配期望尺寸的图片
            matching_images = []
            for filename in single_pc_size_images:
                if expected_suffix.lower() in filename.lower():
                    matching_images.append(filename)
            
            if matching_images:
                return [os.path.join(source_folder, f) for f in matching_images]
            elif base_images:
                return [os.path.join(source_folder, f) for f in base_images]
            else:
                return [os.path.join(source_folder, f) for f in single_pc_size_images]
    
    # 默认返回所有找到的图片
    return [os.path.join(source_folder, f) for f in all_skc_images]

def run_edge_case_test(test_name, skc, files, target_mapping, description):
    """运行边界情况测试"""
    print(f"\n{'='*60}")
    print(f"边界测试: {test_name}")
    print(f"{'='*60}")
    print(f"描述: {description}")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        # 创建测试文件
        for filename in files:
            with open(os.path.join(temp_dir, filename), 'w') as f:
                f.write("test")
        
        print(f"SKC: {skc}")
        print(f"可用图片: {files}")
        print(f"目标映射: {target_mapping}")
        
        # 执行筛选
        result = find_images_by_skc_with_size_filter(temp_dir, skc, target_mapping)
        result_files = [os.path.basename(p) for p in result]
        
        print(f"筛选结果: {result_files}")
        
        # 分析结果
        if not result_files:
            print("⚠️ 未找到任何图片")
        elif len(result_files) == 1:
            print("✅ 找到单个匹配图片")
        else:
            print(f"ℹ️ 找到多个图片 ({len(result_files)}个)")
        
        return result_files

def edge_case_tests():
    """边界情况和特殊场景测试"""
    print("🔍 开始边界情况和特殊场景测试")
    
    # 测试不同长度的SKC
    test_cases = [
        # 9位SKC
        {
            "name": "9位SKC测试",
            "skc": "123456789",
            "files": ["123456789.jpg", "123456789-4060.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "description": "测试9位SKC的处理"
        },
        
        # 11位SKC
        {
            "name": "11位SKC测试",
            "skc": "12345678901",
            "files": ["12345678901.jpg", "12345678901-4060.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "description": "测试11位SKC的处理"
        },
        
        # 大小写混合
        {
            "name": "大小写混合测试",
            "skc": "12345678901",
            "files": ["12345678901.JPG", "12345678901-4060.JPG"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "description": "测试大小写不敏感处理"
        },
        
        # 特殊尺寸格式
        {
            "name": "特殊尺寸格式",
            "skc": "12345678901",
            "files": ["12345678901-4060.jpg", "12345678901-5070.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画60x40cm-彭于晏-俞志敏组-(总X)-店铺号",
            "description": "测试60x40cm（宽高颠倒）的处理"
        },
        
        # 复杂多PC命名
        {
            "name": "复杂多PC命名",
            "skc": "12345678901",
            "files": ["12345678901-1-2-3-4-5-4060.jpg", "12345678901-1-2-3-4-5-5070.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "description": "测试复杂多PC命名格式"
        },
        
        # 无效映射格式
        {
            "name": "无效映射格式",
            "skc": "12345678901",
            "files": ["12345678901.jpg", "12345678901-4060.jpg"],
            "target_mapping": "无效的映射格式没有尺寸信息",
            "description": "测试无效映射格式的处理"
        },
        
        # 空映射
        {
            "name": "空映射测试",
            "skc": "12345678901",
            "files": ["12345678901.jpg", "12345678901-4060.jpg"],
            "target_mapping": "",
            "description": "测试空映射字符串的处理"
        },
        
        # 多个相同尺寸
        {
            "name": "多个相同尺寸",
            "skc": "12345678901",
            "files": ["12345678901-4060.jpg", "12345678901-4060-v2.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "description": "测试多个相同尺寸图片的处理"
        },
        
        # 混合PC格式
        {
            "name": "混合PC格式",
            "skc": "12345678901",
            "files": ["12345678901.jpg", "12345678901-4060.jpg", "12345678901-1-2-4060.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "description": "测试单PC和多PC混合的处理"
        },
        
        # 非标准后缀
        {
            "name": "非标准后缀",
            "skc": "12345678901",
            "files": ["12345678901-custom.jpg", "12345678901-special.jpg"],
            "target_mapping": "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号",
            "description": "测试非标准后缀的处理"
        }
    ]
    
    # 运行所有边界测试
    for test_case in test_cases:
        run_edge_case_test(
            test_case["name"],
            test_case["skc"],
            test_case["files"],
            test_case["target_mapping"],
            test_case["description"]
        )
    
    print(f"\n{'='*60}")
    print(f"边界测试完成")
    print(f"{'='*60}")
    print("所有边界情况都已测试，请检查结果是否符合预期")

if __name__ == "__main__":
    edge_case_tests()
