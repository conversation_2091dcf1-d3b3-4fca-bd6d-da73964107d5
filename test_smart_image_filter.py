#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能图片筛选功能
"""

import os
import re
import logging
import tempfile

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(message)s')

def extract_size_from_target_mapping(target_mapping):
    """
    从目标映射字符串中提取尺寸信息
    """
    if not target_mapping:
        return None, None
    
    # 匹配各种尺寸格式
    size_patterns = [
        r'(\d+)x(\d+)cm',  # 40x60cm
        r'(\d+)X(\d+)cm',  # 40X60cm
        r'(\d+)×(\d+)cm',  # 40×60cm
    ]
    
    for pattern in size_patterns:
        match = re.search(pattern, target_mapping)
        if match:
            width = int(match.group(1))
            height = int(match.group(2))
            logging.info(f"🔍 [尺寸提取] 从映射 '{target_mapping}' 中提取到尺寸: {width}x{height}cm")
            return width, height
    
    logging.warning(f"🔍 [尺寸提取] 无法从映射 '{target_mapping}' 中提取尺寸信息")
    return None, None

def is_multi_pc_image(filename, skc_base):
    """
    判断图片是否为多PC格式（包含序号如-1-2-3）
    """
    if not filename or not skc_base:
        return False
    
    filename_lower = filename.lower()
    skc_lower = skc_base.lower()
    
    # 如果文件名不是以SKC开头，不是多PC
    if not filename_lower.startswith(skc_lower):
        return False
    
    # 提取SKC后面的部分
    suffix = filename_lower[len(skc_lower):]
    
    # 多PC格式：包含多个连续的数字序号，如 -1-2-3040.jpg, -1-2-4060.jpg
    # 匹配模式：-数字-数字-可选尺寸.jpg
    multi_pc_pattern = r'^-\d+-\d+(?:-\d+)*(?:-?\d{4})?\.jpg$'
    
    if re.match(multi_pc_pattern, suffix):
        logging.info(f"🔍 [PC判断] {filename} 识别为多PC格式 (后缀: {suffix})")
        return True
    
    logging.info(f"🔍 [PC判断] {filename} 识别为单PC格式 (后缀: {suffix})")
    return False

def get_size_suffix_from_dimensions(width, height):
    """
    根据尺寸生成对应的文件名后缀
    """
    if width is None or height is None:
        return None
    
    # 生成4位数字后缀
    suffix = f"-{width:02d}{height:02d}"
    logging.info(f"🔍 [后缀生成] 尺寸 {width}x{height}cm -> 后缀 {suffix}")
    return suffix

def find_images_by_skc_with_size_filter(source_folder, skc_base, target_mapping=None):
    """
    查找SKC相关的图片文件，支持单图片多规格的智能筛选
    """
    if not os.path.isdir(source_folder):
        return []
    
    # 查找所有SKC相关的图片
    all_skc_images = []
    for filename in os.listdir(source_folder):
        if (filename.lower().startswith(skc_base.lower()) and 
            filename.lower().endswith('.jpg') and 
            os.path.isfile(os.path.join(source_folder, filename))):
            all_skc_images.append(filename)
    
    if not all_skc_images:
        logging.info(f"🔍 [智能筛选] 未找到SKC {skc_base} 的任何图片")
        return []
    
    logging.info(f"🔍 [智能筛选] 找到SKC {skc_base} 的图片: {all_skc_images}")
    
    # 分类图片：基础图片、单PC多规格图片、多PC图片
    base_images = []  # skc.jpg
    single_pc_size_images = []  # skc-4060.jpg, skc-5070.jpg
    multi_pc_images = []  # skc-1-2-4060.jpg
    
    for filename in all_skc_images:
        if is_multi_pc_image(filename, skc_base):
            multi_pc_images.append(filename)
        elif filename.lower() == f"{skc_base.lower()}.jpg":
            base_images.append(filename)
        else:
            # 检查是否为单PC尺寸格式
            suffix = filename.lower()[len(skc_base.lower()):]
            # 匹配 -4060.jpg, -5070.jpg 等格式（只有尺寸后缀，没有序号）
            if re.match(r'^-\d{4}\.jpg$', suffix):
                single_pc_size_images.append(filename)
            else:
                # 其他格式也归类为单PC
                single_pc_size_images.append(filename)
    
    logging.info(f"🔍 [智能筛选] 图片分类:")
    logging.info(f"🔍 [智能筛选]   基础图片: {base_images}")
    logging.info(f"🔍 [智能筛选]   单PC多规格: {single_pc_size_images}")
    logging.info(f"🔍 [智能筛选]   多PC图片: {multi_pc_images}")
    
    # 如果有多PC图片，优先返回多PC图片（按原有逻辑）
    if multi_pc_images:
        logging.info(f"🔍 [智能筛选] 检测到多PC图片，返回所有多PC图片")
        result_paths = [os.path.join(source_folder, f) for f in multi_pc_images]
        return result_paths
    
    # 如果只有基础图片，直接返回
    if base_images and not single_pc_size_images:
        logging.info(f"🔍 [智能筛选] 只有基础图片，按原方法处理")
        result_paths = [os.path.join(source_folder, f) for f in base_images]
        return result_paths
    
    # 如果有单PC多规格图片，需要根据target_mapping进行筛选
    if single_pc_size_images:
        if not target_mapping:
            logging.info(f"🔍 [智能筛选] 有多规格图片但无目标映射，返回所有图片")
            result_paths = [os.path.join(source_folder, f) for f in base_images + single_pc_size_images]
            return result_paths
        
        # 从目标映射中提取期望的尺寸
        expected_width, expected_height = extract_size_from_target_mapping(target_mapping)
        
        if expected_width is None or expected_height is None:
            logging.info(f"🔍 [智能筛选] 无法从目标映射提取尺寸，返回所有图片")
            result_paths = [os.path.join(source_folder, f) for f in base_images + single_pc_size_images]
            return result_paths
        
        # 生成期望的后缀
        expected_suffix = get_size_suffix_from_dimensions(expected_width, expected_height)
        
        if expected_suffix:
            # 查找匹配期望尺寸的图片
            matching_images = []
            for filename in single_pc_size_images:
                if expected_suffix.lower() in filename.lower():
                    matching_images.append(filename)
                    logging.info(f"🔍 [智能筛选] 找到匹配尺寸的图片: {filename}")
            
            if matching_images:
                logging.info(f"🔍 [智能筛选] 根据尺寸筛选，返回匹配的图片: {matching_images}")
                result_paths = [os.path.join(source_folder, f) for f in matching_images]
                return result_paths
            else:
                logging.info(f"🔍 [智能筛选] 未找到匹配尺寸的图片，检查是否有基础图片")
                if base_images:
                    logging.info(f"🔍 [智能筛选] 有基础图片，返回基础图片: {base_images}")
                    result_paths = [os.path.join(source_folder, f) for f in base_images]
                    return result_paths
                else:
                    logging.info(f"🔍 [智能筛选] 无匹配图片，返回所有单PC图片")
                    result_paths = [os.path.join(source_folder, f) for f in single_pc_size_images]
                    return result_paths
    
    # 默认返回所有找到的图片
    logging.info(f"🔍 [智能筛选] 默认返回所有图片")
    result_paths = [os.path.join(source_folder, f) for f in all_skc_images]
    return result_paths

def test_smart_image_filter():
    """测试智能图片筛选功能"""
    print("="*60)
    print("测试智能图片筛选功能")
    print("="*60)
    
    # 创建临时测试文件夹
    with tempfile.TemporaryDirectory() as temp_dir:
        print(f"测试文件夹: {temp_dir}")
        
        # 测试用例1：只有基础图片
        print("\n测试用例1：只有基础图片")
        print("-"*40)
        skc = "12345678901"
        test_files = [f"{skc}.jpg"]
        
        for filename in test_files:
            with open(os.path.join(temp_dir, filename), 'w') as f:
                f.write("test")
        
        target_mapping = "特级JIT-4-15-上/下午自送-1pc木框帆布画40x60cm-彭于晏-俞志敏组-(总X)-店铺号"
        result = find_images_by_skc_with_size_filter(temp_dir, skc, target_mapping)
        print(f"结果: {[os.path.basename(p) for p in result]}")
        
        # 清理
        for filename in test_files:
            os.remove(os.path.join(temp_dir, filename))
        
        # 测试用例2：基础图片 + 多规格图片
        print("\n测试用例2：基础图片 + 多规格图片")
        print("-"*40)
        test_files = [f"{skc}.jpg", f"{skc}-4060.jpg", f"{skc}-5070.jpg"]
        
        for filename in test_files:
            with open(os.path.join(temp_dir, filename), 'w') as f:
                f.write("test")
        
        result = find_images_by_skc_with_size_filter(temp_dir, skc, target_mapping)
        print(f"结果: {[os.path.basename(p) for p in result]}")
        
        # 清理
        for filename in test_files:
            os.remove(os.path.join(temp_dir, filename))
        
        # 测试用例3：多PC图片
        print("\n测试用例3：多PC图片")
        print("-"*40)
        test_files = [f"{skc}-1-2-4060.jpg", f"{skc}-1-2-3-4060.jpg"]
        
        for filename in test_files:
            with open(os.path.join(temp_dir, filename), 'w') as f:
                f.write("test")
        
        result = find_images_by_skc_with_size_filter(temp_dir, skc, target_mapping)
        print(f"结果: {[os.path.basename(p) for p in result]}")
        
        # 清理
        for filename in test_files:
            os.remove(os.path.join(temp_dir, filename))

if __name__ == "__main__":
    try:
        test_smart_image_filter()
    except Exception as e:
        print(f"测试出错: {e}")
        import traceback
        traceback.print_exc()
